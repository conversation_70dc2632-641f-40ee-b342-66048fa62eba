
This file lists modules PyInstaller was not able to find. This does not
necessarily mean this module is required for running your program. Python and
Python 3rd-party packages include a lot of conditional or optional modules. For
example the module 'ntpath' only exists on Windows, whereas the module
'posixpath' only exists on Posix systems.

Types if import:
* top-level: imported at the top-level - look at these first
* conditional: imported within an if-statement
* delayed: imported within a function
* optional: imported within a try-except-statement

IMPORTANT: Do NOT post this list to the issue-tracker. Use it as a basis for
            tracking down the missing module yourself. Thanks!

missing module named _posixsubprocess - imported by subprocess (optional), multiprocessing.util (delayed)
missing module named _posixshmem - imported by multiprocessing.resource_tracker (conditional), multiprocessing.shared_memory (conditional)
missing module named org - imported by copy (optional)
missing module named _scproxy - imported by urllib.request (conditional)
missing module named termios - imported by tty (top-level), getpass (optional)
missing module named pwd - imported by posixpath (delayed, conditional), shutil (optional), tarfile (optional), pathlib (delayed, conditional, optional), http.server (delayed, optional), webbrowser (delayed), netrc (delayed, conditional), getpass (delayed), distutils.util (delayed, conditional, optional), distutils.archive_util (optional), setuptools._vendor.backports.tarfile (optional), setuptools._distutils.util (delayed, conditional, optional), setuptools._distutils.archive_util (optional)
missing module named 'org.python' - imported by pickle (optional), xml.sax (delayed, conditional)
missing module named 'java.lang' - imported by platform (delayed, optional), xml.sax._exceptions (conditional)
missing module named multiprocessing.BufferTooShort - imported by multiprocessing (top-level), multiprocessing.connection (top-level)
missing module named multiprocessing.AuthenticationError - imported by multiprocessing (top-level), multiprocessing.connection (top-level)
missing module named _frozen_importlib_external - imported by importlib._bootstrap (delayed), importlib (optional), importlib.abc (optional), zipimport (top-level)
excluded module named _frozen_importlib - imported by importlib (optional), importlib.abc (optional), zipimport (top-level)
missing module named posix - imported by os (conditional, optional), shutil (conditional), importlib._bootstrap_external (conditional)
missing module named resource - imported by posix (top-level), numpy.linalg.tests.test_linalg (delayed, conditional)
missing module named grp - imported by shutil (optional), tarfile (optional), pathlib (delayed), distutils.archive_util (optional), setuptools._vendor.backports.tarfile (optional), setuptools._distutils.archive_util (optional)
missing module named multiprocessing.get_context - imported by multiprocessing (top-level), multiprocessing.pool (top-level), multiprocessing.managers (top-level), multiprocessing.sharedctypes (top-level), numpy.lib.tests.test_io (top-level)
missing module named multiprocessing.TimeoutError - imported by multiprocessing (top-level), multiprocessing.pool (top-level)
missing module named multiprocessing.set_start_method - imported by multiprocessing (top-level), multiprocessing.spawn (top-level)
missing module named multiprocessing.get_start_method - imported by multiprocessing (top-level), multiprocessing.spawn (top-level)
missing module named multiprocessing.Value - imported by multiprocessing (top-level), numpy.lib.tests.test_io (top-level)
missing module named pyimod02_importers - imported by D:\anaconda3\envs\win7\Lib\site-packages\PyInstaller\hooks\rthooks\pyi_rth_pkgutil.py (delayed), D:\anaconda3\envs\win7\Lib\site-packages\PyInstaller\hooks\rthooks\pyi_rth_pkgres.py (delayed)
missing module named _manylinux - imported by packaging._manylinux (delayed, optional), setuptools._vendor.packaging._manylinux (delayed, optional), wheel.vendored.packaging._manylinux (delayed, optional)
missing module named 'distutils._modified' - imported by setuptools._distutils.file_util (delayed)
missing module named _aix_support - imported by setuptools._distutils.compat.py38 (delayed, optional)
missing module named 'distutils._log' - imported by setuptools._distutils.command.bdist_dumb (top-level), setuptools._distutils.command.bdist_rpm (top-level), setuptools._distutils.command.build_clib (top-level), setuptools._distutils.command.build_ext (top-level), setuptools._distutils.command.build_py (top-level), setuptools._distutils.command.build_scripts (top-level), setuptools._distutils.command.clean (top-level), setuptools._distutils.command.config (top-level), setuptools._distutils.command.install (top-level), setuptools._distutils.command.install_scripts (top-level), setuptools._distutils.command.sdist (top-level)
missing module named usercustomize - imported by site (delayed, optional)
missing module named sitecustomize - imported by site (delayed, optional)
missing module named readline - imported by cmd (delayed, conditional, optional), code (delayed, conditional, optional), pdb (delayed, optional), site (delayed, optional), rlcompleter (optional)
missing module named 'unittest.mock' - imported by setuptools._distutils._msvccompiler (top-level)
missing module named 'typing.io' - imported by importlib.resources (top-level)
missing module named jaraco.text.yield_lines - imported by setuptools._vendor.jaraco.text (top-level), setuptools._entry_points (top-level), setuptools.command._requirestxt (top-level)
missing module named trove_classifiers - imported by setuptools.config._validate_pyproject.formats (optional)
missing module named tomllib - imported by setuptools.compat.py310 (conditional)
missing module named '_typeshed.importlib' - imported by pkg_resources (conditional)
missing module named _typeshed - imported by setuptools.command.bdist_wheel (conditional), pkg_resources (conditional), jaraco.collections (conditional)
missing module named jnius - imported by setuptools._vendor.platformdirs.android (delayed, conditional, optional)
missing module named android - imported by setuptools._vendor.platformdirs.android (delayed, conditional, optional)
missing module named _pytest - imported by numpy.typing.tests.test_typing (conditional)
missing module named mypy - imported by numpy.typing.mypy_plugin (optional), numpy.typing.tests.test_typing (optional)
missing module named 'mypy.build' - imported by numpy.typing.mypy_plugin (optional)
missing module named 'mypy.nodes' - imported by numpy.typing.mypy_plugin (optional)
missing module named 'mypy.plugin' - imported by numpy.typing.mypy_plugin (optional)
missing module named 'mypy.types' - imported by numpy.typing.mypy_plugin (optional)
missing module named 'numpy.testing.noseclasses' - imported by numpy.testing.tests.test_doctesting (conditional)
missing module named psutil - imported by numpy.testing._private.utils (delayed, optional)
excluded module named unittest - imported by numpy.testing (top-level), doctest (top-level), numpy.testing._private.utils (top-level), numpy.testing._private.parameterized (top-level), numpy.core.tests.test_overrides (top-level), numpy.distutils.tests.test_ccompiler_opt (conditional), numpy.distutils.tests.test_ccompiler_opt_conf (top-level), numpy.ma.testutils (top-level)
missing module named numpy.core.result_type - imported by numpy.core (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.core.float_ - imported by numpy.core (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.core.number - imported by numpy.core (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.core.object_ - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.testing._private.utils (delayed)
missing module named numpy.core.all - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.testing._private.utils (delayed)
missing module named numpy.core.bool_ - imported by numpy.core (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.core.inf - imported by numpy.core (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.core.array2string - imported by numpy.core (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.lib.imag - imported by numpy.lib (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.lib.real - imported by numpy.lib (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.lib.iscomplexobj - imported by numpy.lib (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.core.signbit - imported by numpy.core (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.core.isscalar - imported by numpy.core (delayed), numpy.testing._private.utils (delayed), numpy.lib.polynomial (top-level)
missing module named win32pdh - imported by numpy.testing._private.utils (delayed, conditional)
missing module named numpy.core.isinf - imported by numpy.core (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.core.errstate - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.testing._private.utils (delayed)
missing module named numpy.core.isfinite - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.testing._private.utils (delayed)
missing module named numpy.core.isnan - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.testing._private.utils (delayed)
missing module named numpy.core.array - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.testing._private.utils (top-level), numpy.lib.polynomial (top-level), numpy.core.tests.test_shape_base (top-level), numpy.lib.user_array (top-level), numpy.polynomial.tests.test_printing (top-level), numpy.polynomial.tests.test_symbol (top-level)
missing module named numpy.core.isnat - imported by numpy.core (top-level), numpy.testing._private.utils (top-level)
missing module named numpy.core.ndarray - imported by numpy.core (top-level), numpy.testing._private.utils (top-level), numpy.lib.utils (top-level)
missing module named numpy.core.array_repr - imported by numpy.core (top-level), numpy.testing._private.utils (top-level)
missing module named numpy.core.arange - imported by numpy.core (top-level), numpy.testing._private.utils (top-level), numpy.fft.helper (top-level), numpy.core.tests.test_shape_base (top-level), numpy.fft.tests.test_helper (delayed), numpy.lib.tests.test_utils (top-level), numpy.lib.user_array (top-level), numpy.polynomial.tests.test_printing (top-level)
missing module named numpy.core.empty - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.testing._private.utils (top-level), numpy.fft.helper (top-level)
missing module named numpy.core.float32 - imported by numpy.core (top-level), numpy.testing._private.utils (top-level)
missing module named numpy.core.intp - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.testing._private.utils (top-level)
missing module named 'unittest.case' - imported by numpy.testing._private.utils (top-level)
missing module named 'Cython.Compiler' - imported by numpy.random.tests.test_extending (optional)
missing module named cython - imported by numpy.core.tests.test_cython (optional), numpy.random.tests.test_extending (optional)
missing module named numba - imported by pandas.core.util.numba_ (delayed, conditional), pandas.core.window.numba_ (delayed, conditional), pandas.core.window.online (delayed, conditional), pandas.core._numba.executor (delayed, conditional), pandas.core._numba.kernels.mean_ (top-level), pandas.core._numba.kernels.shared (top-level), pandas.core._numba.kernels.min_max_ (top-level), pandas.core._numba.kernels.sum_ (top-level), pandas.core._numba.kernels.var_ (top-level), pandas.core.groupby.numba_ (delayed, conditional), pandas.tests.frame.test_ufunc (delayed), pandas.tests.groupby.aggregate.test_numba (delayed, conditional), pandas.tests.groupby.transform.test_numba (delayed, conditional), pandas.tests.window.test_numba (delayed, conditional), numpy.random.tests.test_extending (optional), numpy.random._examples.numba.extending (top-level)
missing module named cffi - imported by win32ctypes.core (optional), numpy.random.tests.test_direct (optional), numpy.random.tests.test_extending (optional), numpy.random._examples.cffi.extending (top-level)
missing module named numpy.uint64 - imported by numpy (top-level), numpy.array_api._typing (top-level), numpy.random.mtrand (top-level), numpy.random.bit_generator (top-level), numpy.random._philox (top-level), numpy.random._sfc64 (top-level), numpy.random._generator (top-level)
missing module named numpy.uint32 - imported by numpy (top-level), numpy.array_api._typing (top-level), numpy.random.mtrand (top-level), numpy.random.bit_generator (top-level), numpy.random._generator (top-level), numpy.random._mt19937 (top-level)
missing module named numpy.uint16 - imported by numpy (top-level), numpy.array_api._typing (top-level), numpy.random.mtrand (top-level), numpy.random._generator (top-level), numpy.core.tests.test_half (top-level)
missing module named numpy.uint8 - imported by numpy (top-level), numpy.array_api._typing (top-level), numpy.random.mtrand (top-level), numpy.random._generator (top-level)
missing module named numpy.uint - imported by numpy (top-level), numpy.random.mtrand (top-level), numpy.random._generator (top-level)
missing module named numpy.ndarray - imported by numpy (top-level), numpy._typing._array_like (top-level), numpy.ma.core (top-level), numpy.ma.extras (top-level), numpy.ma.mrecords (top-level), numpy.random.mtrand (top-level), numpy.random.bit_generator (top-level), numpy.random._philox (top-level), numpy.random._sfc64 (top-level), numpy.random._generator (top-level), numpy.random._mt19937 (top-level), numpy.ctypeslib (top-level), pandas.compat.numpy.function (top-level), numpy.core.tests.test_function_base (top-level), numpy.core.tests.test_memmap (top-level), numpy.lib.recfunctions (top-level), numpy.ma.testutils (top-level), numpy.ma.tests.test_core (top-level)
missing module named numpy.int64 - imported by numpy (top-level), numpy.array_api._typing (top-level), numpy.random.mtrand (top-level), numpy.random._generator (top-level)
missing module named numpy.int32 - imported by numpy (top-level), numpy.array_api._typing (top-level), numpy.random.mtrand (top-level), numpy.random._generator (top-level)
missing module named numpy.int16 - imported by numpy (top-level), numpy.array_api._typing (top-level), numpy.random.mtrand (top-level), numpy.random._generator (top-level)
missing module named numpy.int8 - imported by numpy (top-level), numpy.array_api._typing (top-level), numpy.random.mtrand (top-level), numpy.random._generator (top-level)
missing module named numpy.float64 - imported by numpy (top-level), numpy.array_api._typing (top-level), numpy.random.mtrand (top-level), numpy.random._generator (top-level), numpy.core.tests.test_half (top-level), numpy.linalg.tests.test_regression (top-level)
missing module named numpy.float32 - imported by numpy (top-level), numpy.array_api._typing (top-level), numpy.random.mtrand (top-level), numpy.random._generator (top-level), numpy.core.tests.test_half (top-level)
missing module named numpy.dtype - imported by numpy (top-level), numpy._typing._array_like (top-level), numpy.array_api._typing (top-level), numpy.ma.mrecords (top-level), numpy.random.mtrand (top-level), numpy.random.bit_generator (top-level), numpy.random._philox (top-level), numpy.random._sfc64 (top-level), numpy.random._generator (top-level), numpy.random._mt19937 (top-level), numpy.ctypeslib (top-level), numpy.core.tests.test_function_base (top-level)
missing module named numpy.bool_ - imported by numpy (top-level), numpy._typing._array_like (top-level), numpy.ma.core (top-level), numpy.ma.mrecords (top-level), numpy.random.mtrand (top-level), numpy.random._generator (top-level)
missing module named numpy.recarray - imported by numpy (top-level), numpy.ma.mrecords (top-level), numpy.lib.recfunctions (top-level), numpy.ma.tests.test_mrecords (top-level)
missing module named numpy.array - imported by numpy (top-level), numpy.ma.core (top-level), numpy.ma.extras (top-level), numpy.ma.mrecords (top-level), numpy.core.tests.test_function_base (top-level), numpy.core.tests.test_machar (top-level), numpy.core.tests.test_nditer (top-level), numpy.f2py.tests.test_return_character (top-level), numpy.f2py.tests.test_return_complex (top-level), numpy.f2py.tests.test_return_integer (top-level), numpy.f2py.tests.test_return_logical (top-level), numpy.f2py.tests.test_return_real (top-level), numpy.lib.tests.test_twodim_base (top-level), numpy.linalg.tests.test_linalg (top-level), numpy.linalg.tests.test_regression (top-level)
missing module named numpy.expand_dims - imported by numpy (top-level), numpy.ma.core (top-level)
missing module named numpy.iscomplexobj - imported by numpy (top-level), numpy.ma.core (top-level)
missing module named numpy.amin - imported by numpy (top-level), numpy.ma.core (top-level)
missing module named numpy.amax - imported by numpy (top-level), numpy.ma.core (top-level)
missing module named numpy.core.swapaxes - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.fft._pocketfft (top-level), numpy.linalg.tests.test_linalg (top-level)
missing module named numpy.cdouble - imported by numpy (top-level), numpy.linalg.tests.test_linalg (top-level)
missing module named numpy.csingle - imported by numpy (top-level), numpy.linalg.tests.test_linalg (top-level)
missing module named numpy.double - imported by numpy (top-level), numpy.core.tests.test_getlimits (top-level), numpy.linalg.tests.test_linalg (top-level)
missing module named numpy.single - imported by numpy (top-level), numpy.core.tests.test_getlimits (top-level), numpy.linalg.tests.test_linalg (top-level)
missing module named numpy.core.reciprocal - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.sort - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.argsort - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.sign - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.count_nonzero - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.divide - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.lib.user_array (top-level)
missing module named numpy.core.matmul - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.asanyarray - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.atleast_2d - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.core.tests.test_shape_base (top-level)
missing module named numpy.core.product - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.amax - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.amin - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.moveaxis - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.geterrobj - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.finfo - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.lib.polynomial (top-level), numpy.core.tests.test_getlimits (top-level)
missing module named numpy.core.sum - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.sqrt - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.fft._pocketfft (top-level), numpy.lib.user_array (top-level)
missing module named numpy.core.multiply - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.lib.user_array (top-level)
missing module named numpy.core.add - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.lib.user_array (top-level)
missing module named numpy.core.dot - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.lib.polynomial (top-level)
missing module named numpy.core.Inf - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.newaxis - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.core.tests.test_shape_base (top-level)
missing module named numpy.core.complexfloating - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.inexact - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.cdouble - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.csingle - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.double - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.single - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.intc - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.empty_like - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.zeros - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.fft._pocketfft (top-level)
missing module named numpy.core.asarray - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.lib.utils (top-level), numpy.fft._pocketfft (top-level), numpy.fft.helper (top-level), numpy.fft.tests.test_helper (delayed), numpy.lib.user_array (top-level)
missing module named threadpoolctl - imported by numpy.lib.utils (delayed, optional)
missing module named numpy.core.ufunc - imported by numpy.core (top-level), numpy.lib.utils (top-level)
missing module named numpy.core.transpose - imported by numpy.core (top-level), numpy.lib.function_base (top-level), numpy.lib.user_array (top-level)
missing module named numpy.core.greater_equal - imported by numpy.core (top-level), numpy.lib.user_array (top-level)
missing module named numpy.core.greater - imported by numpy.core (top-level), numpy.lib.user_array (top-level)
missing module named numpy.core.equal - imported by numpy.core (top-level), numpy.lib.user_array (top-level)
missing module named numpy.core.not_equal - imported by numpy.core (top-level), numpy.lib.user_array (top-level)
missing module named numpy.core.less_equal - imported by numpy.core (top-level), numpy.lib.user_array (top-level)
missing module named numpy.core.less - imported by numpy.core (top-level), numpy.lib.user_array (top-level)
missing module named numpy.core.bitwise_xor - imported by numpy.core (top-level), numpy.lib.user_array (top-level)
missing module named numpy.core.bitwise_or - imported by numpy.core (top-level), numpy.lib.user_array (top-level)
missing module named numpy.core.bitwise_and - imported by numpy.core (top-level), numpy.lib.user_array (top-level)
missing module named numpy.core.right_shift - imported by numpy.core (top-level), numpy.lib.user_array (top-level)
missing module named numpy.core.left_shift - imported by numpy.core (top-level), numpy.lib.user_array (top-level)
missing module named numpy.core.power - imported by numpy.core (top-level), numpy.lib.user_array (top-level)
missing module named numpy.core.remainder - imported by numpy.core (top-level), numpy.lib.user_array (top-level)
missing module named numpy.core.subtract - imported by numpy.core (top-level), numpy.lib.user_array (top-level)
missing module named numpy.histogramdd - imported by numpy (delayed), numpy.lib.twodim_base (delayed)
missing module named numpy.core.iinfo - imported by numpy.core (top-level), numpy.lib.twodim_base (top-level), numpy.core.tests.test_getlimits (top-level)
missing module named numpy.eye - imported by numpy (delayed), numpy.core.numeric (delayed), numpy.lib.tests.test_twodim_base (top-level)
missing module named 'hypothesis.strategies' - imported by numpy.core.tests.test_scalarmath (top-level), numpy.lib.tests.test_function_base (top-level)
missing module named 'hypothesis.extra' - imported by pandas._testing._hypothesis (top-level), numpy.array_api.tests.test_set_functions (top-level), numpy.core.tests.test_arrayprint (top-level), numpy.core.tests.test_dtype (top-level), numpy.core.tests.test_numeric (top-level), numpy.core.tests.test_scalarmath (top-level), numpy.lib.tests.test_function_base (top-level)
missing module named hypothesis - imported by pandas._testing._hypothesis (top-level), pandas.conftest (top-level), pandas.tests.frame.indexing.test_where (top-level), pandas.tests.indexes.ranges.test_setops (top-level), pandas.tests.io.parser.test_parse_dates (top-level), pandas.tests.io.sas.test_byteswap (top-level), pandas.tests.scalar.timedelta.test_timedelta (top-level), pandas.tests.scalar.timestamp.test_timestamp (top-level), pandas.tests.scalar.timestamp.test_unary_ops (top-level), pandas.tests.tseries.offsets.test_offsets_properties (top-level), pandas.tests.tseries.offsets.test_ticks (top-level), pandas.tests.tslibs.test_ccalendar (top-level), numpy.array_api.tests.test_set_functions (top-level), numpy.conftest (top-level), numpy.core.tests.test_arrayprint (top-level), numpy.core.tests.test_dtype (top-level), numpy.core.tests.test_numeric (top-level), numpy.core.tests.test_scalarmath (top-level), numpy.lib.tests.test_function_base (top-level)
missing module named numpy.core.atleast_3d - imported by numpy.core (top-level), numpy.lib.shape_base (top-level), numpy.core.tests.test_shape_base (top-level)
missing module named numpy.core.vstack - imported by numpy.core (top-level), numpy.lib.shape_base (top-level), numpy.core.tests.test_shape_base (top-level)
missing module named numpy.core.ones - imported by numpy.core (top-level), numpy.lib.polynomial (top-level)
missing module named numpy.core.hstack - imported by numpy.core (top-level), numpy.lib.polynomial (top-level), numpy.core.tests.test_shape_base (top-level)
missing module named numpy.core.atleast_1d - imported by numpy.core (top-level), numpy.lib.polynomial (top-level), numpy.core.tests.test_shape_base (top-level)
missing module named numpy.core.linspace - imported by numpy.core (top-level), numpy.lib.index_tricks (top-level)
missing module named numpy.core.integer - imported by numpy.core (top-level), numpy.fft.helper (top-level)
missing module named numpy.core.conjugate - imported by numpy.core (top-level), numpy.fft._pocketfft (top-level)
missing module named _uuid - imported by uuid (optional)
missing module named netbios - imported by uuid (delayed)
missing module named win32wnet - imported by uuid (delayed)
missing module named __version__ - imported by numpy.f2py.setup (top-level)
missing module named 'numpy_distutils.cpuinfo' - imported by numpy.f2py.diagnose (delayed, conditional, optional)
missing module named 'numpy_distutils.fcompiler' - imported by numpy.f2py.diagnose (delayed, conditional, optional)
missing module named 'numpy_distutils.command' - imported by numpy.f2py.diagnose (delayed, conditional, optional)
missing module named numpy_distutils - imported by numpy.f2py.diagnose (delayed, optional)
missing module named charset_normalizer - imported by numpy.f2py.crackfortran (optional)
missing module named ccompiler_opt - imported by numpy.distutils.tests.test_ccompiler_opt (conditional), numpy.distutils.tests.test_ccompiler_opt_conf (conditional)
missing module named numarray - imported by numpy.distutils.system_info (delayed, conditional, optional)
missing module named Numeric - imported by numpy.distutils.system_info (delayed, conditional, optional)
missing module named _curses - imported by curses (top-level), curses.has_key (top-level)
missing module named numpy.integer - imported by numpy (top-level), numpy._typing._array_like (top-level), numpy.ctypeslib (top-level)
missing module named mem_policy - imported by numpy.core.tests.test_mem_policy (delayed, optional)
missing module named asyncio.DefaultEventLoopPolicy - imported by asyncio (delayed, conditional), asyncio.events (delayed, conditional)
missing module named numpy.float16 - imported by numpy (top-level), numpy.core.tests.test_half (top-level)
missing module named numpy.longdouble - imported by numpy (top-level), numpy.core.tests.test_getlimits (top-level)
missing module named numpy.half - imported by numpy (top-level), numpy.core.tests.test_getlimits (top-level)
missing module named sets - imported by pytz.tzinfo (optional)
missing module named collections.Mapping - imported by collections (optional), pytz.lazy (optional)
missing module named UserDict - imported by pytz.lazy (optional)
missing module named checks - imported by numpy.core.tests.test_cython (delayed)
missing module named Cython - imported by numpy.core.tests.test_cython (optional)
missing module named array_interface_testing - imported by numpy.core.tests.test_array_interface (delayed, optional)
missing module named setup_common - imported by numpy.core.setup (top-level)
missing module named numpy_api - imported by numpy.core.generate_numpy_api (top-level)
missing module named genapi - imported by numpy.core.generate_numpy_api (top-level)
missing module named 'code_generators.numpy_api' - imported by numpy.core.cversions (top-level)
missing module named code_generators - imported by numpy.core.cversions (top-level)
missing module named pickle5 - imported by numpy.compat.py3k (optional)
missing module named numpy.bytes_ - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.str_ - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.void - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.object_ - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.datetime64 - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.timedelta64 - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.number - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.complexfloating - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.floating - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.unsignedinteger - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.generic - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy._typing._ufunc - imported by numpy._typing (conditional)
missing module named numpy.ufunc - imported by numpy (top-level), numpy._typing (top-level)
missing module named 'macholib.mach_o' - imported by PyInstaller.utils.osx (delayed, conditional)
missing module named 'macholib.util' - imported by PyInstaller.utils.osx (top-level)
missing module named 'macholib.MachO' - imported by PyInstaller.utils.osx (top-level)
missing module named macholib - imported by PyInstaller.utils.osx (top-level)
missing module named win32ctypes.core._time - imported by win32ctypes.core (top-level), win32ctypes.pywin32.win32api (top-level)
missing module named win32ctypes.core._system_information - imported by win32ctypes.core (top-level), win32ctypes.pywin32.win32api (top-level)
missing module named win32ctypes.core._resource - imported by win32ctypes.core (top-level), win32ctypes.pywin32.win32api (top-level)
missing module named win32ctypes.core._dll - imported by win32ctypes.core (top-level), win32ctypes.pywin32.win32api (top-level)
missing module named win32ctypes.core._common - imported by win32ctypes.core (top-level), win32ctypes.pywin32.win32api (top-level), win32ctypes.pywin32.win32cred (top-level)
missing module named win32ctypes.core._authentication - imported by win32ctypes.core (top-level), win32ctypes.pywin32.win32cred (top-level)
missing module named pytest - imported by pandas._testing._io (delayed), pandas._testing (delayed), pandas.conftest (top-level), pandas.util._test_decorators (top-level), pandas.tests.api.test_api (top-level), pandas.tests.apply.conftest (top-level), pandas.tests.apply.test_frame_apply (top-level), pandas.tests.apply.test_frame_apply_relabeling (top-level), pandas.tests.apply.test_frame_transform (top-level), pandas.tests.apply.test_invalid_arg (top-level), pandas.tests.apply.test_series_apply (top-level), pandas.tests.apply.test_series_transform (top-level), pandas.tests.apply.test_str (top-level), pandas.tests.arithmetic.common (top-level), pandas.tests.arithmetic.conftest (top-level), pandas.tests.arithmetic.test_array_ops (top-level), pandas.tests.arithmetic.test_datetime64 (top-level), pandas.tests.arithmetic.test_interval (top-level), pandas.tests.arithmetic.test_numeric (top-level), pandas.tests.arithmetic.test_object (top-level), pandas.tests.arithmetic.test_period (top-level), pandas.tests.arithmetic.test_timedelta64 (top-level), pandas.tests.arrays.boolean.test_arithmetic (top-level), pandas.tests.arrays.boolean.test_astype (top-level), pandas.tests.arrays.boolean.test_comparison (top-level), pandas.tests.arrays.masked_shared (top-level), pandas.tests.extension.base.accumulate (top-level), pandas.tests.extension.base.casting (top-level), pandas.tests.extension.base.constructors (top-level), pandas.tests.extension.base.dim2 (top-level), pandas.tests.extension.base.dtype (top-level), pandas.tests.extension.base.getitem (top-level), pandas.tests.extension.base.groupby (top-level), pandas.tests.extension.base.io (top-level), pandas.tests.extension.base.methods (top-level), pandas.tests.extension.base.missing (top-level), pandas.tests.extension.base.ops (top-level), pandas.tests.extension.base.printing (top-level), pandas.tests.extension.base.reduce (top-level), pandas.tests.extension.base.reshaping (top-level), pandas.tests.extension.base.setitem (top-level), pandas.tests.arrays.boolean.test_construction (top-level), pandas.tests.arrays.boolean.test_function (top-level), pandas.tests.arrays.boolean.test_indexing (top-level), pandas.tests.arrays.boolean.test_logical (top-level), pandas.tests.arrays.boolean.test_reduction (top-level), pandas.tests.arrays.categorical.conftest (top-level), pandas.tests.arrays.categorical.test_algos (top-level), pandas.tests.arrays.categorical.test_analytics (top-level), pandas.tests.arrays.categorical.test_api (top-level), pandas.tests.arrays.categorical.test_astype (top-level), pandas.tests.arrays.categorical.test_constructors (top-level), pandas.tests.arrays.categorical.test_dtypes (top-level), pandas.tests.arrays.categorical.test_indexing (top-level), pandas.tests.arrays.categorical.test_missing (top-level), pandas.tests.arrays.categorical.test_operators (top-level), pandas.tests.arrays.categorical.test_replace (top-level), pandas.tests.arrays.categorical.test_sorting (top-level), pandas.tests.arrays.categorical.test_take (top-level), pandas.tests.arrays.categorical.test_warnings (top-level), pandas.tests.arrays.datetimes.test_constructors (top-level), pandas.tests.arrays.datetimes.test_cumulative (top-level), pandas.tests.arrays.datetimes.test_reductions (top-level), pandas.tests.arrays.floating.conftest (top-level), pandas.tests.arrays.floating.test_arithmetic (top-level), pandas.tests.arrays.floating.test_astype (top-level), pandas.tests.arrays.floating.test_comparison (top-level), pandas.tests.arrays.floating.test_concat (top-level), pandas.tests.arrays.floating.test_construction (top-level), pandas.tests.arrays.floating.test_function (top-level), pandas.tests.arrays.floating.test_repr (top-level), pandas.tests.arrays.floating.test_to_numpy (top-level), pandas.tests.arrays.integer.conftest (top-level), pandas.tests.arrays.integer.test_arithmetic (top-level), pandas.tests.arrays.integer.test_comparison (top-level), pandas.tests.arrays.integer.test_concat (top-level), pandas.tests.arrays.integer.test_construction (top-level), pandas.tests.arrays.integer.test_dtypes (top-level), pandas.tests.arrays.integer.test_function (top-level), pandas.tests.arrays.integer.test_repr (top-level), pandas.tests.arrays.interval.test_astype (top-level), pandas.tests.arrays.interval.test_interval (top-level), pandas.tests.arrays.interval.test_ops (top-level), pandas.tests.arrays.masked.test_arithmetic (top-level), pandas.tests.arrays.masked.test_arrow_compat (top-level), pandas.tests.arrays.masked.test_function (top-level), pandas.tests.arrays.masked.test_indexing (top-level), pandas.tests.arrays.numpy_.test_numpy (top-level), pandas.tests.arrays.period.test_arrow_compat (top-level), pandas.tests.arrays.period.test_astype (top-level), pandas.tests.arrays.period.test_constructors (top-level), pandas.tests.arrays.period.test_reductions (top-level), pandas.tests.arrays.sparse.test_accessor (top-level), pandas.tests.arrays.sparse.test_arithmetics (top-level), pandas.tests.arrays.sparse.test_array (top-level), pandas.tests.arrays.sparse.test_astype (top-level), pandas.tests.arrays.sparse.test_combine_concat (top-level), pandas.tests.arrays.sparse.test_constructors (top-level), pandas.tests.arrays.sparse.test_dtype (top-level), pandas.tests.arrays.sparse.test_indexing (top-level), pandas.tests.arrays.sparse.test_libsparse (top-level), pandas.tests.arrays.sparse.test_reductions (top-level), pandas.tests.arrays.sparse.test_unary (top-level), pandas.tests.arrays.string_.test_string (top-level), pandas.tests.arrays.string_.test_string_arrow (top-level), pandas.tests.arrays.test_array (top-level), pandas.tests.arrays.test_datetimelike (top-level), pandas.tests.arrays.test_datetimes (top-level), pandas.tests.arrays.test_period (top-level), pandas.tests.arrays.test_timedeltas (top-level), pandas.tests.arrays.timedeltas.test_constructors (top-level), pandas.tests.arrays.timedeltas.test_cumulative (top-level), pandas.tests.arrays.timedeltas.test_reductions (top-level), pandas.tests.base.test_constructors (top-level), pandas.tests.base.test_conversion (top-level), pandas.tests.base.test_fillna (top-level), pandas.tests.base.test_misc (top-level), pandas.tests.base.test_transpose (top-level), pandas.tests.base.test_unique (top-level), pandas.tests.base.test_value_counts (top-level), pandas.tests.computation.test_compat (top-level), pandas.tests.computation.test_eval (top-level), pandas.tests.config.test_config (top-level), pandas.tests.config.test_localization (top-level), pandas.tests.copy_view.index.test_datetimeindex (top-level), pandas.tests.copy_view.index.test_index (top-level), pandas.tests.copy_view.index.test_periodindex (top-level), pandas.tests.copy_view.index.test_timedeltaindex (top-level), pandas.tests.copy_view.test_array (top-level), pandas.tests.copy_view.test_astype (top-level), pandas.tests.copy_view.test_constructors (top-level), pandas.tests.copy_view.test_core_functionalities (top-level), pandas.tests.copy_view.test_functions (top-level), pandas.tests.copy_view.test_indexing (top-level), pandas.tests.copy_view.test_internals (top-level), pandas.tests.copy_view.test_interp_fillna (top-level), pandas.tests.copy_view.test_methods (top-level), pandas.tests.copy_view.test_replace (top-level), pandas.tests.dtypes.cast.test_construct_from_scalar (top-level), pandas.tests.dtypes.cast.test_construct_ndarray (top-level), pandas.tests.dtypes.cast.test_construct_object_arr (top-level), pandas.tests.dtypes.cast.test_downcast (top-level), pandas.tests.dtypes.cast.test_find_common_type (top-level), pandas.tests.dtypes.cast.test_infer_datetimelike (top-level), pandas.tests.dtypes.cast.test_infer_dtype (top-level), pandas.tests.dtypes.cast.test_maybe_box_native (top-level), pandas.tests.dtypes.cast.test_promote (top-level), pandas.tests.dtypes.test_common (top-level), pandas.tests.dtypes.test_concat (top-level), pandas.tests.dtypes.test_dtypes (top-level), pandas.tests.dtypes.test_generic (top-level), pandas.tests.dtypes.test_inference (top-level), pandas.tests.dtypes.test_missing (top-level), pandas.tests.extension.conftest (top-level), pandas.tests.extension.decimal.test_decimal (top-level), pandas.tests.extension.json.test_json (top-level), pandas.tests.extension.list.test_list (top-level), pandas.tests.extension.test_arrow (top-level), pandas.tests.extension.test_boolean (top-level), pandas.tests.extension.test_categorical (top-level), pandas.tests.extension.test_common (top-level), pandas.tests.extension.test_datetime (top-level), pandas.tests.extension.test_extension (top-level), pandas.tests.extension.test_external_block (top-level), pandas.tests.extension.test_floating (top-level), pandas.tests.extension.test_integer (top-level), pandas.tests.extension.test_interval (top-level), pandas.tests.extension.test_numpy (top-level), pandas.tests.extension.test_period (top-level), pandas.tests.extension.test_sparse (top-level), pandas.tests.extension.test_string (top-level), pandas.tests.frame.conftest (top-level), pandas.tests.frame.constructors.test_from_dict (top-level), pandas.tests.frame.constructors.test_from_records (top-level), pandas.tests.frame.indexing.test_coercion (top-level), pandas.tests.frame.indexing.test_delitem (top-level), pandas.tests.frame.indexing.test_get (top-level), pandas.tests.frame.indexing.test_get_value (top-level), pandas.tests.frame.indexing.test_getitem (top-level), pandas.tests.frame.indexing.test_indexing (top-level), pandas.tests.frame.indexing.test_insert (top-level), pandas.tests.frame.indexing.test_setitem (top-level), pandas.tests.frame.indexing.test_take (top-level), pandas.tests.frame.indexing.test_where (top-level), pandas.tests.frame.indexing.test_xs (top-level), pandas.tests.frame.methods.test_add_prefix_suffix (top-level), pandas.tests.frame.methods.test_align (top-level), pandas.tests.frame.methods.test_asfreq (top-level), pandas.tests.frame.methods.test_asof (top-level), pandas.tests.frame.methods.test_assign (top-level), pandas.tests.frame.methods.test_astype (top-level), pandas.tests.frame.methods.test_at_time (top-level), pandas.tests.frame.methods.test_between_time (top-level), pandas.tests.frame.methods.test_clip (top-level), pandas.tests.frame.methods.test_combine (top-level), pandas.tests.frame.methods.test_combine_first (top-level), pandas.tests.frame.methods.test_compare (top-level), pandas.tests.frame.methods.test_convert_dtypes (top-level), pandas.tests.frame.methods.test_copy (top-level), pandas.tests.frame.methods.test_cov_corr (top-level), pandas.tests.frame.methods.test_describe (top-level), pandas.tests.frame.methods.test_diff (top-level), pandas.tests.frame.methods.test_dot (top-level), pandas.tests.frame.methods.test_drop (top-level), pandas.tests.frame.methods.test_drop_duplicates (top-level), pandas.tests.frame.methods.test_droplevel (top-level), pandas.tests.frame.methods.test_dropna (top-level), pandas.tests.frame.methods.test_dtypes (top-level), pandas.tests.frame.methods.test_duplicated (top-level), pandas.tests.frame.methods.test_explode (top-level), pandas.tests.frame.methods.test_fillna (top-level), pandas.tests.frame.methods.test_filter (top-level), pandas.tests.frame.methods.test_first_and_last (top-level), pandas.tests.frame.methods.test_first_valid_index (top-level), pandas.tests.frame.methods.test_interpolate (top-level), pandas.tests.frame.methods.test_is_homogeneous_dtype (top-level), pandas.tests.frame.methods.test_isin (top-level), pandas.tests.frame.methods.test_join (top-level), pandas.tests.frame.methods.test_matmul (top-level), pandas.tests.frame.methods.test_nlargest (top-level), pandas.tests.frame.methods.test_pct_change (top-level), pandas.tests.frame.methods.test_pipe (top-level), pandas.tests.frame.methods.test_quantile (top-level), pandas.tests.frame.methods.test_rank (top-level), pandas.tests.frame.methods.test_reindex (top-level), pandas.tests.frame.methods.test_reindex_like (top-level), pandas.tests.frame.methods.test_rename (top-level), pandas.tests.frame.methods.test_rename_axis (top-level), pandas.tests.frame.methods.test_reorder_levels (top-level), pandas.tests.frame.methods.test_replace (top-level), pandas.tests.frame.methods.test_reset_index (top-level), pandas.tests.frame.methods.test_round (top-level), pandas.tests.frame.methods.test_sample (top-level), pandas.tests.frame.methods.test_select_dtypes (top-level), pandas.tests.frame.methods.test_set_axis (top-level), pandas.tests.frame.methods.test_set_index (top-level), pandas.tests.frame.methods.test_shift (top-level), pandas.tests.frame.methods.test_sort_index (top-level), pandas.tests.frame.methods.test_sort_values (top-level), pandas.tests.frame.methods.test_swapaxes (top-level), pandas.tests.frame.methods.test_swaplevel (top-level), pandas.tests.frame.methods.test_to_csv (top-level), pandas.tests.frame.methods.test_to_dict (top-level), pandas.tests.frame.methods.test_to_dict_of_blocks (top-level), pandas.tests.frame.methods.test_to_period (top-level), pandas.tests.frame.methods.test_to_records (top-level), pandas.tests.frame.methods.test_to_timestamp (top-level), pandas.tests.frame.methods.test_transpose (top-level), pandas.tests.frame.methods.test_truncate (top-level), pandas.tests.frame.methods.test_tz_convert (top-level), pandas.tests.frame.methods.test_tz_localize (top-level), pandas.tests.frame.methods.test_update (top-level), pandas.tests.frame.methods.test_value_counts (top-level), pandas.tests.frame.methods.test_values (top-level), pandas.tests.frame.test_api (top-level), pandas.tests.frame.test_arithmetic (top-level), pandas.tests.frame.test_block_internals (top-level), pandas.tests.frame.test_constructors (top-level), pandas.tests.frame.test_cumulative (top-level), pandas.tests.frame.test_logical_ops (top-level), pandas.tests.frame.test_nonunique_indexes (top-level), pandas.tests.frame.test_query_eval (top-level), pandas.tests.frame.test_reductions (top-level), pandas.tests.frame.test_repr_info (top-level), pandas.tests.frame.test_stack_unstack (top-level), pandas.tests.frame.test_subclass (top-level), pandas.tests.frame.test_ufunc (top-level), pandas.tests.frame.test_unary (top-level), pandas.tests.frame.test_validate (top-level), pandas.tests.generic.test_duplicate_labels (top-level), pandas.tests.generic.test_finalize (top-level), pandas.tests.generic.test_frame (top-level), pandas.tests.generic.test_generic (top-level), pandas.tests.generic.test_label_or_level_utils (top-level), pandas.tests.generic.test_series (top-level), pandas.tests.generic.test_to_xarray (top-level), pandas.tests.groupby.aggregate.test_aggregate (top-level), pandas.tests.groupby.aggregate.test_cython (top-level), pandas.tests.groupby.aggregate.test_numba (top-level), pandas.tests.groupby.aggregate.test_other (top-level), pandas.tests.groupby.conftest (top-level), pandas.tests.groupby.test_allowlist (top-level), pandas.tests.groupby.test_any_all (top-level), pandas.tests.groupby.test_api_consistency (top-level), pandas.tests.groupby.test_apply (top-level), pandas.tests.groupby.test_bin_groupby (top-level), pandas.tests.groupby.test_categorical (top-level), pandas.tests.groupby.test_counting (top-level), pandas.tests.groupby.test_filters (top-level), pandas.tests.groupby.test_function (top-level), pandas.tests.groupby.test_groupby (top-level), pandas.tests.groupby.test_groupby_dropna (top-level), pandas.tests.groupby.test_groupby_shift_diff (top-level), pandas.tests.groupby.test_groupby_subclass (top-level), pandas.tests.groupby.test_grouping (top-level), pandas.tests.groupby.test_index_as_string (top-level), pandas.tests.groupby.test_indexing (top-level), pandas.tests.groupby.test_libgroupby (top-level), pandas.tests.groupby.test_min_max (top-level), pandas.tests.groupby.test_missing (top-level), pandas.tests.groupby.test_nth (top-level), pandas.tests.groupby.test_numba (top-level), pandas.tests.groupby.test_nunique (top-level), pandas.tests.groupby.test_quantile (top-level), pandas.tests.groupby.test_raises (top-level), pandas.tests.groupby.test_rank (top-level), pandas.tests.groupby.test_sample (top-level), pandas.tests.groupby.test_size (top-level), pandas.tests.groupby.test_timegrouper (top-level), pandas.tests.groupby.test_value_counts (top-level), pandas.tests.groupby.transform.test_numba (top-level), pandas.tests.groupby.transform.test_transform (top-level), pandas.tests.indexes.base_class.test_constructors (top-level), pandas.tests.indexes.base_class.test_formats (top-level), pandas.tests.indexes.base_class.test_indexing (top-level), pandas.tests.indexes.base_class.test_reshape (top-level), pandas.tests.indexes.base_class.test_setops (top-level), pandas.tests.indexes.categorical.test_append (top-level), pandas.tests.indexes.categorical.test_astype (top-level), pandas.tests.indexes.categorical.test_category (top-level), pandas.tests.indexes.common (top-level), pandas.tests.indexes.categorical.test_constructors (top-level), pandas.tests.indexes.categorical.test_equals (top-level), pandas.tests.indexes.categorical.test_fillna (top-level), pandas.tests.indexes.categorical.test_indexing (top-level), pandas.tests.indexes.categorical.test_map (top-level), pandas.tests.indexes.categorical.test_reindex (top-level), pandas.tests.indexes.conftest (top-level), pandas.tests.indexes.datetimelike (top-level), pandas.tests.indexes.datetimelike_.test_drop_duplicates (top-level), pandas.tests.indexes.datetimelike_.test_equals (top-level), pandas.tests.indexes.datetimelike_.test_indexing (top-level), pandas.tests.indexes.datetimelike_.test_nat (top-level), pandas.tests.indexes.datetimelike_.test_sort_values (top-level), pandas.tests.indexes.datetimes.methods.test_astype (top-level), pandas.tests.indexes.datetimes.methods.test_factorize (top-level), pandas.tests.indexes.datetimes.methods.test_fillna (top-level), pandas.tests.indexes.datetimes.methods.test_insert (top-level), pandas.tests.indexes.datetimes.methods.test_repeat (top-level), pandas.tests.indexes.datetimes.methods.test_shift (top-level), pandas.tests.indexes.datetimes.methods.test_snap (top-level), pandas.tests.indexes.datetimes.methods.test_to_period (top-level), pandas.tests.indexes.datetimes.test_constructors (top-level), pandas.tests.indexes.datetimes.test_date_range (top-level), pandas.tests.indexes.datetimes.test_timezones (top-level), pandas.tests.indexes.datetimes.test_datetime (top-level), pandas.tests.indexes.datetimes.test_datetimelike (top-level), pandas.tests.indexes.datetimes.test_delete (top-level), pandas.tests.indexes.datetimes.test_formats (top-level), pandas.tests.indexes.datetimes.test_freq_attr (top-level), pandas.tests.indexes.datetimes.test_indexing (top-level), pandas.tests.indexes.datetimes.test_join (top-level), pandas.tests.indexes.datetimes.test_map (top-level), pandas.tests.indexes.datetimes.test_misc (top-level), pandas.tests.indexes.datetimes.test_ops (top-level), pandas.tests.indexes.datetimes.test_partial_slicing (top-level), pandas.tests.indexes.datetimes.test_pickle (top-level), pandas.tests.indexes.datetimes.test_scalar_compat (top-level), pandas.tests.indexes.datetimes.test_setops (top-level), pandas.tests.indexes.interval.test_astype (top-level), pandas.tests.indexes.interval.test_base (top-level), pandas.tests.indexes.interval.test_constructors (top-level), pandas.tests.indexes.interval.test_formats (top-level), pandas.tests.indexes.interval.test_indexing (top-level), pandas.tests.indexes.interval.test_interval (top-level), pandas.tests.indexes.interval.test_interval_range (top-level), pandas.tests.indexes.interval.test_interval_tree (top-level), pandas.tests.indexes.interval.test_join (top-level), pandas.tests.indexes.interval.test_pickle (top-level), pandas.tests.indexes.interval.test_setops (top-level), pandas.tests.indexes.multi.conftest (top-level), pandas.tests.indexes.multi.test_analytics (top-level), pandas.tests.indexes.multi.test_astype (top-level), pandas.tests.indexes.multi.test_compat (top-level), pandas.tests.indexes.multi.test_constructors (top-level), pandas.tests.indexes.multi.test_conversion (top-level), pandas.tests.indexes.multi.test_copy (top-level), pandas.tests.indexes.multi.test_drop (top-level), pandas.tests.indexes.multi.test_duplicates (top-level), pandas.tests.indexes.multi.test_equivalence (top-level), pandas.tests.indexes.multi.test_formats (top-level), pandas.tests.indexes.multi.test_get_set (top-level), pandas.tests.indexes.multi.test_indexing (top-level), pandas.tests.indexes.multi.test_integrity (top-level), pandas.tests.indexes.multi.test_isin (top-level), pandas.tests.indexes.multi.test_join (top-level), pandas.tests.indexes.multi.test_missing (top-level), pandas.tests.indexes.multi.test_monotonic (top-level), pandas.tests.indexes.multi.test_names (top-level), pandas.tests.indexes.multi.test_partial_indexing (top-level), pandas.tests.indexes.multi.test_pickle (top-level), pandas.tests.indexes.multi.test_reindex (top-level), pandas.tests.indexes.multi.test_reshape (top-level), pandas.tests.indexes.multi.test_setops (top-level), pandas.tests.indexes.multi.test_sorting (top-level), pandas.tests.indexes.multi.test_take (top-level), pandas.tests.indexes.numeric.test_astype (top-level), pandas.tests.indexes.numeric.test_indexing (top-level), pandas.tests.indexes.numeric.test_join (top-level), pandas.tests.indexes.numeric.test_numeric (top-level), pandas.tests.indexes.numeric.test_setops (top-level), pandas.tests.indexes.object.test_astype (top-level), pandas.tests.indexes.object.test_indexing (top-level), pandas.tests.indexes.period.methods.test_asfreq (top-level), pandas.tests.indexes.period.methods.test_astype (top-level), pandas.tests.indexes.period.methods.test_insert (top-level), pandas.tests.indexes.period.methods.test_is_full (top-level), pandas.tests.indexes.period.methods.test_repeat (top-level), pandas.tests.indexes.period.methods.test_shift (top-level), pandas.tests.indexes.period.methods.test_to_timestamp (top-level), pandas.tests.indexes.period.test_constructors (top-level), pandas.tests.indexes.period.test_formats (top-level), pandas.tests.indexes.period.test_freq_attr (top-level), pandas.tests.indexes.period.test_indexing (top-level), pandas.tests.indexes.period.test_join (top-level), pandas.tests.indexes.period.test_partial_slicing (top-level), pandas.tests.indexes.period.test_period (top-level), pandas.tests.indexes.period.test_period_range (top-level), pandas.tests.indexes.period.test_pickle (top-level), pandas.tests.indexes.period.test_resolution (top-level), pandas.tests.indexes.period.test_searchsorted (top-level), pandas.tests.indexes.period.test_tools (top-level), pandas.tests.indexes.ranges.test_constructors (top-level), pandas.tests.indexes.ranges.test_indexing (top-level), pandas.tests.indexes.ranges.test_range (top-level), pandas.tests.indexes.ranges.test_setops (top-level), pandas.tests.indexes.test_any_index (top-level), pandas.tests.indexes.test_base (top-level), pandas.tests.indexes.test_common (top-level), pandas.tests.indexes.test_engines (top-level), pandas.tests.indexes.test_frozen (top-level), pandas.tests.indexes.test_index_new (top-level), pandas.tests.indexes.test_indexing (top-level), pandas.tests.indexes.test_numpy_compat (top-level), pandas.tests.indexes.test_setops (top-level), pandas.tests.indexes.timedeltas.methods.test_astype (top-level), pandas.tests.indexes.timedeltas.methods.test_insert (top-level), pandas.tests.indexes.timedeltas.methods.test_shift (top-level), pandas.tests.indexes.timedeltas.test_constructors (top-level), pandas.tests.indexes.timedeltas.test_formats (top-level), pandas.tests.indexes.timedeltas.test_freq_attr (top-level), pandas.tests.indexes.timedeltas.test_indexing (top-level), pandas.tests.indexes.timedeltas.test_scalar_compat (top-level), pandas.tests.indexes.timedeltas.test_searchsorted (top-level), pandas.tests.indexes.timedeltas.test_setops (top-level), pandas.tests.indexes.timedeltas.test_timedelta (top-level), pandas.tests.indexes.timedeltas.test_timedelta_range (top-level), pandas.tests.indexing.conftest (top-level), pandas.tests.indexing.interval.test_interval (top-level), pandas.tests.indexing.interval.test_interval_new (top-level), pandas.tests.indexing.multiindex.test_chaining_and_caching (top-level), pandas.tests.indexing.multiindex.test_getitem (top-level), pandas.tests.indexing.multiindex.test_iloc (top-level), pandas.tests.indexing.multiindex.test_indexing_slow (top-level), pandas.tests.indexing.multiindex.test_loc (top-level), pandas.tests.indexing.multiindex.test_multiindex (top-level), pandas.tests.indexing.multiindex.test_partial (top-level), pandas.tests.indexing.multiindex.test_setitem (top-level), pandas.tests.indexing.multiindex.test_slice (top-level), pandas.tests.indexing.multiindex.test_sorted (top-level), pandas.tests.indexing.test_at (top-level), pandas.tests.indexing.test_categorical (top-level), pandas.tests.indexing.test_chaining_and_caching (top-level), pandas.tests.indexing.test_check_indexer (top-level), pandas.tests.indexing.test_coercion (top-level), pandas.tests.indexing.test_datetime (top-level), pandas.tests.indexing.test_floats (top-level), pandas.tests.indexing.test_iloc (top-level), pandas.tests.indexing.test_indexers (top-level), pandas.tests.indexing.test_indexing (top-level), pandas.tests.indexing.test_loc (top-level), pandas.tests.indexing.test_na_indexing (top-level), pandas.tests.indexing.test_partial (top-level), pandas.tests.indexing.test_scalar (top-level), pandas.tests.interchange.conftest (top-level), pandas.tests.interchange.test_impl (top-level), pandas.tests.interchange.test_spec_conformance (top-level), pandas.tests.interchange.test_utils (top-level), pandas.tests.internals.test_internals (top-level), pandas.tests.io.conftest (top-level), pandas.tests.io.excel.conftest (top-level), pandas.tests.io.excel.test_odf (top-level), pandas.tests.io.excel.test_odswriter (top-level), pandas.tests.io.excel.test_openpyxl (top-level), pandas.tests.io.excel.test_readers (top-level), pandas.tests.io.excel.test_style (top-level), pandas.tests.io.excel.test_writers (top-level), pandas.tests.io.excel.test_xlrd (top-level), pandas.tests.io.excel.test_xlsxwriter (top-level), pandas.tests.io.formats.style.test_bar (top-level), pandas.tests.io.formats.style.test_exceptions (top-level), pandas.tests.io.formats.style.test_format (top-level), pandas.tests.io.formats.style.test_highlight (top-level), pandas.tests.io.formats.style.test_html (top-level), pandas.tests.io.formats.style.test_matplotlib (top-level), pandas.tests.io.formats.style.test_non_unique (top-level), pandas.tests.io.formats.style.test_style (top-level), pandas.tests.io.formats.style.test_to_latex (top-level), pandas.tests.io.formats.style.test_to_string (top-level), pandas.tests.io.formats.style.test_tooltip (top-level), pandas.tests.io.formats.test_console (top-level), pandas.tests.io.formats.test_css (top-level), pandas.tests.io.formats.test_format (top-level), pandas.tests.io.formats.test_info (top-level), pandas.tests.io.formats.test_series_info (top-level), pandas.tests.io.formats.test_to_csv (top-level), pandas.tests.io.test_compression (top-level), pandas.tests.io.formats.test_to_excel (top-level), pandas.tests.io.formats.test_to_html (top-level), pandas.tests.io.formats.test_to_latex (top-level), pandas.tests.io.formats.test_to_markdown (top-level), pandas.tests.io.formats.test_to_string (top-level), pandas.tests.io.json.conftest (top-level), pandas.tests.io.json.test_compression (top-level), pandas.tests.io.json.test_json_table_schema (top-level), pandas.tests.io.json.test_json_table_schema_ext_dtype (top-level), pandas.tests.io.json.test_normalize (top-level), pandas.tests.io.json.test_pandas (top-level), pandas.tests.io.json.test_readlines (top-level), pandas.tests.io.json.test_ujson (top-level), pandas.tests.io.parser.common.test_chunksize (top-level), pandas.tests.io.parser.common.test_common_basic (top-level), pandas.tests.io.parser.common.test_data_list (top-level), pandas.tests.io.parser.common.test_decimal (top-level), pandas.tests.io.parser.common.test_file_buffer_url (top-level), pandas.tests.io.parser.common.test_float (top-level), pandas.tests.io.parser.common.test_index (top-level), pandas.tests.io.parser.common.test_inf (top-level), pandas.tests.io.parser.common.test_ints (top-level), pandas.tests.io.parser.common.test_iterator (top-level), pandas.tests.io.parser.common.test_read_errors (top-level), pandas.tests.io.parser.common.test_verbose (top-level), pandas.tests.io.parser.conftest (top-level), pandas.tests.io.parser.dtypes.test_categorical (top-level), pandas.tests.io.parser.dtypes.test_dtypes_basic (top-level), pandas.tests.io.parser.dtypes.test_empty (top-level), pandas.tests.io.parser.test_c_parser_only (top-level), pandas.tests.io.parser.test_comment (top-level), pandas.tests.io.parser.test_compression (top-level), pandas.tests.io.parser.test_concatenate_chunks (top-level), pandas.tests.io.parser.test_converters (top-level), pandas.tests.io.parser.test_dialect (top-level), pandas.tests.io.parser.test_encoding (top-level), pandas.tests.io.parser.test_header (top-level), pandas.tests.io.parser.test_index_col (top-level), pandas.tests.io.parser.test_mangle_dupes (top-level), pandas.tests.io.parser.test_multi_thread (top-level), pandas.tests.io.parser.test_na_values (top-level), pandas.tests.io.parser.test_network (top-level), pandas.tests.io.parser.test_parse_dates (top-level), pandas.tests.io.parser.test_python_parser_only (top-level), pandas.tests.io.parser.test_quoting (top-level), pandas.tests.io.parser.test_read_fwf (top-level), pandas.tests.io.parser.test_skiprows (top-level), pandas.tests.io.parser.test_textreader (top-level), pandas.tests.io.parser.test_unsupported (top-level), pandas.tests.io.parser.test_upcast (top-level), pandas.tests.io.parser.usecols.test_parse_dates (top-level), pandas.tests.io.parser.usecols.test_strings (top-level), pandas.tests.io.parser.usecols.test_usecols_basic (top-level), pandas.tests.io.pytables.common (top-level), pandas.tests.io.pytables.conftest (top-level), pandas.tests.io.pytables.test_append (top-level), pandas.tests.io.pytables.test_categorical (top-level), pandas.tests.io.pytables.test_compat (top-level), pandas.tests.io.pytables.test_complex (top-level), pandas.tests.io.pytables.test_errors (top-level), pandas.tests.io.pytables.test_file_handling (top-level), pandas.tests.io.pytables.test_keys (top-level), pandas.tests.io.pytables.test_put (top-level), pandas.tests.io.pytables.test_pytables_missing (top-level), pandas.tests.io.pytables.test_read (top-level), pandas.tests.io.pytables.test_retain_attributes (top-level), pandas.tests.io.pytables.test_round_trip (top-level), pandas.tests.io.pytables.test_select (top-level), pandas.tests.io.pytables.test_store (top-level), pandas.tests.io.pytables.test_subclass (top-level), pandas.tests.io.pytables.test_time_series (top-level), pandas.tests.io.pytables.test_timezones (top-level), pandas.tests.io.sas.test_byteswap (top-level), pandas.tests.io.sas.test_sas (top-level), pandas.tests.io.sas.test_sas7bdat (top-level), pandas.tests.io.sas.test_xport (top-level), pandas.tests.io.test_clipboard (top-level), pandas.tests.io.test_common (top-level), pandas.tests.io.test_feather (top-level), pandas.tests.io.test_fsspec (top-level), pandas.tests.io.test_gcs (top-level), pandas.tests.io.test_html (top-level), pandas.tests.io.test_orc (top-level), pandas.tests.io.test_parquet (top-level), pandas.tests.io.test_pickle (top-level), pandas.tests.io.test_s3 (top-level), pandas.tests.io.test_spss (top-level), pandas.tests.io.test_sql (top-level), pandas.tests.io.test_stata (top-level), pandas.tests.io.test_user_agent (top-level), pandas.tests.io.xml.test_to_xml (top-level), pandas.tests.io.xml.test_xml (top-level), pandas.tests.io.xml.test_xml_dtypes (top-level), pandas.tests.libs.test_hashtable (top-level), pandas.tests.libs.test_join (top-level), pandas.tests.libs.test_lib (top-level), pandas.tests.plotting.conftest (top-level), pandas.tests.plotting.frame.test_frame (top-level), pandas.tests.plotting.frame.test_frame_color (top-level), pandas.tests.plotting.frame.test_frame_groupby (top-level), pandas.tests.plotting.frame.test_frame_legend (top-level), pandas.tests.plotting.frame.test_frame_subplots (top-level), pandas.tests.plotting.frame.test_hist_box_by (top-level), pandas.tests.plotting.test_backend (top-level), pandas.tests.plotting.test_boxplot_method (top-level), pandas.tests.plotting.test_common (top-level), pandas.tests.plotting.test_converter (top-level), pandas.tests.plotting.test_datetimelike (top-level), pandas.tests.plotting.test_groupby (top-level), pandas.tests.plotting.test_hist_method (top-level), pandas.tests.plotting.test_misc (top-level), pandas.tests.plotting.test_series (top-level), pandas.tests.plotting.test_style (top-level), pandas.tests.reductions.test_reductions (top-level), pandas.tests.reductions.test_stat_reductions (top-level), pandas.tests.resample.conftest (top-level), pandas.tests.resample.test_base (top-level), pandas.tests.resample.test_datetime_index (top-level), pandas.tests.resample.test_period_index (top-level), pandas.tests.resample.test_resample_api (top-level), pandas.tests.resample.test_resampler_grouper (top-level), pandas.tests.resample.test_time_grouper (top-level), pandas.tests.resample.test_timedelta (top-level), pandas.tests.reshape.concat.conftest (top-level), pandas.tests.reshape.concat.test_append (top-level), pandas.tests.reshape.concat.test_append_common (top-level), pandas.tests.reshape.concat.test_concat (top-level), pandas.tests.reshape.concat.test_dataframe (top-level), pandas.tests.reshape.concat.test_datetimes (top-level), pandas.tests.reshape.concat.test_empty (top-level), pandas.tests.reshape.concat.test_index (top-level), pandas.tests.reshape.concat.test_invalid (top-level), pandas.tests.reshape.concat.test_series (top-level), pandas.tests.reshape.concat.test_sort (top-level), pandas.tests.reshape.merge.test_join (top-level), pandas.tests.reshape.merge.test_merge (top-level), pandas.tests.reshape.merge.test_merge_asof (top-level), pandas.tests.reshape.merge.test_merge_cross (top-level), pandas.tests.reshape.merge.test_merge_index_as_string (top-level), pandas.tests.reshape.merge.test_merge_ordered (top-level), pandas.tests.reshape.merge.test_multi (top-level), pandas.tests.reshape.test_crosstab (top-level), pandas.tests.reshape.test_cut (top-level), pandas.tests.reshape.test_from_dummies (top-level), pandas.tests.reshape.test_get_dummies (top-level), pandas.tests.reshape.test_melt (top-level), pandas.tests.reshape.test_pivot (top-level), pandas.tests.reshape.test_pivot_multilevel (top-level), pandas.tests.reshape.test_qcut (top-level), pandas.tests.reshape.test_union_categoricals (top-level), pandas.tests.reshape.test_util (top-level), pandas.tests.scalar.interval.test_arithmetic (top-level), pandas.tests.scalar.interval.test_interval (top-level), pandas.tests.scalar.interval.test_ops (top-level), pandas.tests.scalar.period.test_asfreq (top-level), pandas.tests.scalar.period.test_period (top-level), pandas.tests.scalar.test_na_scalar (top-level), pandas.tests.scalar.test_nat (top-level), pandas.tests.scalar.timedelta.test_arithmetic (top-level), pandas.tests.scalar.timedelta.test_constructors (top-level), pandas.tests.scalar.timedelta.test_formats (top-level), pandas.tests.scalar.timedelta.test_timedelta (top-level), pandas.tests.scalar.timestamp.test_arithmetic (top-level), pandas.tests.scalar.timestamp.test_comparisons (top-level), pandas.tests.scalar.timestamp.test_constructors (top-level), pandas.tests.scalar.timestamp.test_formats (top-level), pandas.tests.scalar.timestamp.test_rendering (top-level), pandas.tests.scalar.timestamp.test_timestamp (top-level), pandas.tests.scalar.timestamp.test_timezones (top-level), pandas.tests.scalar.timestamp.test_unary_ops (top-level), pandas.tests.series.accessors.test_cat_accessor (top-level), pandas.tests.series.accessors.test_dt_accessor (top-level), pandas.tests.series.accessors.test_str_accessor (top-level), pandas.tests.series.indexing.test_datetime (top-level), pandas.tests.series.indexing.test_delitem (top-level), pandas.tests.series.indexing.test_get (top-level), pandas.tests.series.indexing.test_getitem (top-level), pandas.tests.series.indexing.test_indexing (top-level), pandas.tests.series.indexing.test_mask (top-level), pandas.tests.series.indexing.test_setitem (top-level), pandas.tests.series.indexing.test_take (top-level), pandas.tests.series.indexing.test_where (top-level), pandas.tests.series.indexing.test_xs (top-level), pandas.tests.series.methods.test_add_prefix_suffix (top-level), pandas.tests.series.methods.test_align (top-level), pandas.tests.series.methods.test_argsort (top-level), pandas.tests.series.methods.test_asof (top-level), pandas.tests.series.methods.test_astype (top-level), pandas.tests.series.methods.test_between (top-level), pandas.tests.series.methods.test_clip (top-level), pandas.tests.series.methods.test_compare (top-level), pandas.tests.series.methods.test_convert_dtypes (top-level), pandas.tests.series.methods.test_copy (top-level), pandas.tests.series.methods.test_cov_corr (top-level), pandas.tests.series.methods.test_describe (top-level), pandas.tests.series.methods.test_diff (top-level), pandas.tests.series.methods.test_drop (top-level), pandas.tests.series.methods.test_drop_duplicates (top-level), pandas.tests.series.methods.test_dropna (top-level), pandas.tests.series.methods.test_duplicated (top-level), pandas.tests.series.methods.test_equals (top-level), pandas.tests.series.methods.test_explode (top-level), pandas.tests.series.methods.test_fillna (top-level), pandas.tests.series.methods.test_interpolate (top-level), pandas.tests.series.methods.test_is_unique (top-level), pandas.tests.series.methods.test_isin (top-level), pandas.tests.series.methods.test_item (top-level), pandas.tests.series.methods.test_matmul (top-level), pandas.tests.series.methods.test_nlargest (top-level), pandas.tests.series.methods.test_pct_change (top-level), pandas.tests.series.methods.test_quantile (top-level), pandas.tests.series.methods.test_rank (top-level), pandas.tests.series.methods.test_reindex (top-level), pandas.tests.series.methods.test_rename (top-level), pandas.tests.series.methods.test_rename_axis (top-level), pandas.tests.series.methods.test_repeat (top-level), pandas.tests.series.methods.test_replace (top-level), pandas.tests.series.methods.test_reset_index (top-level), pandas.tests.series.methods.test_round (top-level), pandas.tests.series.methods.test_searchsorted (top-level), pandas.tests.series.methods.test_sort_index (top-level), pandas.tests.series.methods.test_sort_values (top-level), pandas.tests.series.methods.test_to_csv (top-level), pandas.tests.series.methods.test_to_dict (top-level), pandas.tests.series.methods.test_to_numpy (top-level), pandas.tests.series.methods.test_tolist (top-level), pandas.tests.series.methods.test_truncate (top-level), pandas.tests.series.methods.test_tz_localize (top-level), pandas.tests.series.methods.test_unstack (top-level), pandas.tests.series.methods.test_update (top-level), pandas.tests.series.methods.test_value_counts (top-level), pandas.tests.series.methods.test_values (top-level), pandas.tests.series.methods.test_view (top-level), pandas.tests.series.test_api (top-level), pandas.tests.series.test_arithmetic (top-level), pandas.tests.series.test_constructors (top-level), pandas.tests.series.test_cumulative (top-level), pandas.tests.series.test_logical_ops (top-level), pandas.tests.series.test_missing (top-level), pandas.tests.series.test_reductions (top-level), pandas.tests.series.test_repr (top-level), pandas.tests.series.test_subclass (top-level), pandas.tests.series.test_ufunc (top-level), pandas.tests.series.test_unary (top-level), pandas.tests.series.test_validate (top-level), pandas.tests.strings.conftest (top-level), pandas.tests.strings.test_api (top-level), pandas.tests.strings.test_case_justify (top-level), pandas.tests.strings.test_cat (top-level), pandas.tests.strings.test_extract (top-level), pandas.tests.strings.test_find_replace (top-level), pandas.tests.strings.test_split_partition (top-level), pandas.tests.strings.test_string_array (top-level), pandas.tests.strings.test_strings (top-level), pandas.tests.test_aggregation (top-level), pandas.tests.test_algos (top-level), pandas.tests.test_common (top-level), pandas.tests.test_downstream (top-level), pandas.tests.test_errors (top-level), pandas.tests.test_expressions (top-level), pandas.tests.test_flags (top-level), pandas.tests.test_multilevel (top-level), pandas.tests.test_nanops (top-level), pandas.tests.test_optional_dependency (top-level), pandas.tests.test_register_accessor (top-level), pandas.tests.test_sorting (top-level), pandas.tests.test_take (top-level), pandas.tests.tools.test_to_datetime (top-level), pandas.tests.tools.test_to_numeric (top-level), pandas.tests.tools.test_to_time (top-level), pandas.tests.tools.test_to_timedelta (top-level), pandas.tests.tseries.frequencies.test_freq_code (top-level), pandas.tests.tseries.frequencies.test_frequencies (top-level), pandas.tests.tseries.frequencies.test_inference (top-level), pandas.tests.tseries.holiday.test_calendar (top-level), pandas.tests.tseries.holiday.test_holiday (top-level), pandas.tests.tseries.holiday.test_observance (top-level), pandas.tests.tseries.offsets.conftest (top-level), pandas.tests.tseries.offsets.test_business_day (top-level), pandas.tests.tseries.offsets.test_business_hour (top-level), pandas.tests.tseries.offsets.test_business_month (top-level), pandas.tests.tseries.offsets.test_business_quarter (top-level), pandas.tests.tseries.offsets.test_business_year (top-level), pandas.tests.tseries.offsets.test_common (top-level), pandas.tests.tseries.offsets.test_custom_business_day (top-level), pandas.tests.tseries.offsets.test_custom_business_hour (top-level), pandas.tests.tseries.offsets.test_custom_business_month (top-level), pandas.tests.tseries.offsets.test_offsets (top-level), pandas.tests.tseries.offsets.test_dst (top-level), pandas.tests.tseries.offsets.test_easter (top-level), pandas.tests.tseries.offsets.test_fiscal (top-level), pandas.tests.tseries.offsets.test_index (top-level), pandas.tests.tseries.offsets.test_month (top-level), pandas.tests.tseries.offsets.test_offsets_properties (top-level), pandas.tests.tseries.offsets.test_quarter (top-level), pandas.tests.tseries.offsets.test_ticks (top-level), pandas.tests.tseries.offsets.test_week (top-level), pandas.tests.tseries.offsets.test_year (top-level), pandas.tests.tslibs.test_array_to_datetime (top-level), pandas.tests.tslibs.test_ccalendar (top-level), pandas.tests.tslibs.test_conversion (top-level), pandas.tests.tslibs.test_fields (top-level), pandas.tests.tslibs.test_libfrequencies (top-level), pandas.tests.tslibs.test_liboffsets (top-level), pandas.tests.tslibs.test_np_datetime (top-level), pandas.tests.tslibs.test_parse_iso8601 (top-level), pandas.tests.tslibs.test_parsing (top-level), pandas.tests.tslibs.test_period_asfreq (top-level), pandas.tests.tslibs.test_timedeltas (top-level), pandas.tests.tslibs.test_timezones (top-level), pandas.tests.tslibs.test_to_offset (top-level), pandas.tests.tslibs.test_tzconversion (top-level), pandas.tests.util.conftest (top-level), pandas.tests.util.test_assert_almost_equal (top-level), pandas.tests.util.test_assert_attr_equal (top-level), pandas.tests.util.test_assert_categorical_equal (top-level), pandas.tests.util.test_assert_extension_array_equal (top-level), pandas.tests.util.test_assert_frame_equal (top-level), pandas.tests.util.test_assert_index_equal (top-level), pandas.tests.util.test_assert_interval_array_equal (top-level), pandas.tests.util.test_assert_numpy_array_equal (top-level), pandas.tests.util.test_assert_produces_warning (top-level), pandas.tests.util.test_assert_series_equal (top-level), pandas.tests.util.test_deprecate (top-level), pandas.tests.util.test_deprecate_kwarg (top-level), pandas.tests.util.test_hashing (top-level), pandas.tests.util.test_numba (top-level), pandas.tests.util.test_rewrite_warning (top-level), pandas.tests.util.test_safe_import (top-level), pandas.tests.util.test_str_methods (top-level), pandas.tests.util.test_util (top-level), pandas.tests.util.test_validate_args (top-level), pandas.tests.util.test_validate_args_and_kwargs (top-level), pandas.tests.util.test_validate_inclusive (top-level), pandas.tests.util.test_validate_kwargs (top-level), pandas.tests.window.conftest (top-level), pandas.tests.window.moments.conftest (top-level), pandas.tests.window.moments.test_moments_consistency_ewm (top-level), pandas.tests.window.moments.test_moments_consistency_expanding (top-level), pandas.tests.window.moments.test_moments_consistency_rolling (top-level), pandas.tests.window.test_api (top-level), pandas.tests.window.test_apply (top-level), pandas.tests.window.test_base_indexer (top-level), pandas.tests.window.test_cython_aggregations (top-level), pandas.tests.window.test_dtypes (top-level), pandas.tests.window.test_ewm (top-level), pandas.tests.window.test_expanding (top-level), pandas.tests.window.test_groupby (top-level), pandas.tests.window.test_numba (top-level), pandas.tests.window.test_online (top-level), pandas.tests.window.test_pairwise (top-level), pandas.tests.window.test_rolling (top-level), pandas.tests.window.test_rolling_functions (top-level), pandas.tests.window.test_rolling_quantile (top-level), pandas.tests.window.test_rolling_skew_kurt (top-level), pandas.tests.window.test_timeseries_window (top-level), pandas.tests.window.test_win_type (top-level)
missing module named 'matplotlib.pyplot' - imported by pandas.io.formats.style (optional), pandas._testing._io (delayed), pandas._testing.asserters (delayed), pandas.tests.plotting.common (delayed), pandas.plotting._matplotlib.tools (delayed), pandas.plotting._matplotlib.style (delayed), pandas.plotting._matplotlib.misc (delayed), pandas.plotting._matplotlib.core (delayed), pandas.plotting._matplotlib.boxplot (delayed), pandas.plotting._matplotlib.hist (delayed), pandas.plotting._matplotlib (delayed), pandas.tests.plotting.frame.test_frame (delayed), pandas.tests.plotting.frame.test_frame_color (delayed), pandas.tests.plotting.frame.test_frame_subplots (delayed), pandas.tests.plotting.test_boxplot_method (delayed), pandas.tests.plotting.test_datetimelike (delayed), pandas.tests.plotting.test_hist_method (delayed), pandas.tests.plotting.test_style (delayed), pandas.util._doctools (delayed)
excluded module named matplotlib - imported by pandas.io.formats.style (optional), pandas.tests.io.formats.style.test_matplotlib (top-level), pandas.tests.plotting.common (delayed), pandas.plotting._matplotlib.core (top-level), pandas.plotting._matplotlib.tools (top-level), pandas.plotting._matplotlib.misc (top-level), pandas.plotting._matplotlib.style (top-level), pandas.plotting._matplotlib.timeseries (delayed), pandas.tests.plotting.frame.test_frame (delayed), pandas.tests.plotting.frame.test_frame_color (delayed), pandas.tests.plotting.frame.test_frame_legend (delayed), pandas.tests.plotting.test_misc (delayed), pandas.tests.plotting.test_series (delayed), pandas.tests.plotting.test_style (delayed), pandas.util._doctools (delayed)
missing module named six.moves.range - imported by six.moves (top-level), dateutil.rrule (top-level)
runtime module named six.moves - imported by dateutil.tz.tz (top-level), dateutil.tz._factories (top-level), dateutil.tz.win (top-level), dateutil.rrule (top-level)
missing module named dateutil.tz.tzfile - imported by dateutil.tz (top-level), dateutil.zoneinfo (top-level)
missing module named StringIO - imported by six (conditional)
missing module named 'scipy.stats' - imported by pandas.core.nanops (delayed, conditional), pandas.tests.frame.methods.test_rank (delayed), pandas.tests.frame.test_reductions (delayed), pandas.tests.groupby.test_function (delayed), pandas.plotting._matplotlib.misc (delayed, conditional), pandas.plotting._matplotlib.hist (delayed), pandas.tests.reductions.test_stat_reductions (delayed), pandas.tests.series.methods.test_rank (delayed), pandas.tests.test_algos (delayed), pandas.tests.test_nanops (delayed), pandas.tests.window.test_rolling_skew_kurt (delayed)
missing module named xarray - imported by pandas.tests.arrays.test_datetimelike (delayed, conditional), pandas.tests.generic.test_to_xarray (delayed), pandas.tests.test_downstream (delayed), pandas.tests.tools.test_to_datetime (delayed)
missing module named psycopg2 - imported by pandas.tests.tools.test_to_datetime (delayed)
missing module named sklearn - imported by pandas.tests.test_downstream (delayed)
missing module named 'statsmodels.formula' - imported by pandas.tests.test_downstream (delayed)
missing module named statsmodels - imported by pandas.tests.test_downstream (delayed)
missing module named cftime - imported by pandas.tests.test_downstream (delayed)
missing module named 'dask.array' - imported by pandas.tests.test_downstream (delayed, optional)
missing module named 'dask.dataframe' - imported by pandas.tests.test_downstream (delayed, optional)
missing module named scipy - imported by pandas.core.nanops (delayed, conditional), pandas.core.missing (delayed), pandas.conftest (delayed), pandas.tests.series.methods.test_cov_corr (delayed)
missing module named zoneinfo - imported by pandas.conftest (conditional), pandas.tests.arrays.test_datetimes (optional), pandas.tests.indexes.datetimes.test_timezones (optional), pandas.tests.scalar.timestamp.test_constructors (conditional), pandas.tests.scalar.timestamp.test_timezones (optional)
missing module named 'IPython.core' - imported by pandas.io.formats.printing (delayed, conditional), pandas.conftest (delayed), pandas.tests.arrays.categorical.test_warnings (delayed), pandas.tests.frame.test_api (delayed), pandas.tests.indexes.test_base (delayed), pandas.tests.resample.test_resampler_grouper (delayed)
missing module named 'matplotlib.colors' - imported by pandas.io.formats.style (conditional), pandas.tests.io.formats.test_to_excel (delayed), pandas.plotting._matplotlib.style (top-level), pandas.plotting._matplotlib.core (delayed), pandas.tests.plotting.test_style (delayed)
missing module named 'matplotlib.figure' - imported by pandas.plotting._misc (conditional), pandas.plotting._matplotlib.tools (conditional), pandas.plotting._matplotlib.misc (conditional)
missing module named 'matplotlib.axes' - imported by pandas.plotting._core (conditional), pandas.plotting._misc (conditional), pandas.tests.plotting.common (delayed, conditional), pandas.plotting._matplotlib.tools (conditional), pandas.plotting._matplotlib.misc (conditional), pandas.plotting._matplotlib.timeseries (conditional), pandas.plotting._matplotlib.core (delayed, conditional), pandas.plotting._matplotlib.boxplot (conditional), pandas.plotting._matplotlib.hist (conditional)
missing module named 'matplotlib.lines' - imported by pandas.tests.plotting.common (delayed), pandas.plotting._matplotlib.tools (conditional), pandas.plotting._matplotlib.misc (top-level), pandas.plotting._matplotlib.boxplot (conditional), pandas.tests.plotting.frame.test_frame_legend (delayed)
missing module named 'matplotlib.ticker' - imported by pandas.tests.plotting.common (delayed), pandas.plotting._matplotlib.converter (top-level), pandas.plotting._matplotlib.core (delayed)
missing module named 'matplotlib.axis' - imported by pandas.plotting._matplotlib.tools (conditional), pandas.plotting._matplotlib.core (conditional)
missing module named 'matplotlib.artist' - imported by pandas.plotting._matplotlib.boxplot (top-level), pandas.plotting._matplotlib.core (top-level)
missing module named 'matplotlib.units' - imported by pandas.plotting._matplotlib.converter (top-level)
missing module named 'matplotlib.transforms' - imported by pandas.plotting._matplotlib.converter (top-level)
missing module named 'matplotlib.dates' - imported by pandas.plotting._matplotlib.converter (top-level)
missing module named 'matplotlib.table' - imported by pandas.plotting._matplotlib.tools (top-level)
missing module named 'matplotlib.text' - imported by pandas.tests.plotting.test_misc (delayed)
missing module named 'matplotlib.patches' - imported by pandas.tests.plotting.frame.test_frame (delayed), pandas.tests.plotting.test_hist_method (delayed)
missing module named pylab - imported by pandas.tests.plotting.test_hist_method (delayed)
missing module named 'matplotlib.collections' - imported by pandas.tests.plotting.common (delayed), pandas.tests.plotting.frame.test_frame_color (delayed), pandas.tests.plotting.frame.test_frame_legend (delayed)
missing module named cycler - imported by pandas.tests.plotting.frame.test_frame_color (delayed)
missing module named 'matplotlib.gridspec' - imported by pandas.tests.plotting.frame.test_frame (delayed)
missing module named 'mpl_toolkits.axes_grid1' - imported by pandas.tests.plotting.frame.test_frame (delayed)
missing module named mpl_toolkits - imported by pandas.tests.plotting.frame.test_frame (delayed)
missing module named 'lxml.etree' - imported by openpyxl.xml.functions (conditional), pandas.io.xml (delayed), pandas.io.formats.xml (delayed), pandas.io.html (delayed), pandas.tests.io.xml.test_to_xml (delayed), pandas.tests.io.xml.test_xml (delayed)
missing module named s3fs - imported by pandas.tests.io.conftest (delayed), pandas.tests.io.excel.test_readers (delayed), pandas.tests.io.parser.test_network (delayed), pandas.tests.io.xml.test_to_xml (delayed)
missing module named fsspec - imported by pandas.conftest (delayed), pandas.tests.io.test_gcs (delayed), pandas.tests.io.test_user_agent (delayed)
missing module named 'sqlalchemy.orm' - imported by pandas.tests.io.test_sql (delayed)
missing module named 'sqlalchemy.schema' - imported by pandas.io.sql (delayed), pandas.tests.io.test_sql (delayed)
missing module named 'sqlalchemy.engine' - imported by pandas.io.sql (delayed), pandas.tests.io.test_sql (delayed, conditional)
missing module named sqlalchemy - imported by pandas.io.sql (delayed, conditional), pandas.tests.io.test_sql (delayed, conditional, optional)
missing module named 'botocore.response' - imported by pandas.tests.io.test_s3 (delayed)
missing module named 'pyarrow.dataset' - imported by pandas.tests.io.test_parquet (delayed, conditional)
missing module named 'pyarrow.parquet' - imported by pandas.io.parquet (delayed), pandas.tests.io.test_parquet (delayed, conditional)
missing module named fastparquet - imported by pandas.tests.io.test_parquet (delayed, optional)
missing module named pyarrow - imported by pandas.core.arrays.masked (delayed), pandas.core.arrays.numeric (delayed, conditional), pandas.core.arrays.arrow._arrow_utils (top-level), pandas.core.arrays.string_ (delayed, conditional), pandas.core.arrays.boolean (delayed, conditional), pandas.core.arrays.string_arrow (conditional), pandas.core.arrays.interval (delayed), pandas.core.arrays.arrow.extension_types (top-level), pandas.core.arrays.period (delayed), pandas.core.arrays.arrow.dtype (conditional), pandas.core.strings.accessor (delayed, conditional), pandas.io.parsers.base_parser (delayed, conditional), pandas.core.methods.describe (delayed, conditional), pandas.io.feather_format (delayed), pandas.core.indexes.base (delayed, conditional), pandas.core.arrays.arrow.array (conditional), pandas.core.dtypes.dtypes (delayed, conditional), pandas.compat.pyarrow (optional), pandas._testing (conditional), pandas.conftest (optional), pandas.tests.arrays.interval.test_interval (delayed), pandas.tests.arrays.string_.test_string (delayed, conditional), pandas.tests.arrays.string_.test_string_arrow (delayed), pandas.tests.extension.test_string (delayed), pandas.tests.indexes.multi.test_constructors (delayed), pandas.tests.io.excel.test_readers (delayed, conditional), pandas.tests.io.parser.conftest (delayed, conditional), pandas.tests.io.parser.test_upcast (delayed), pandas.tests.io.test_common (delayed), pandas.tests.io.test_html (delayed, conditional), pandas.tests.io.test_orc (top-level), pandas.tests.io.test_parquet (delayed, optional)
missing module named bs4 - imported by pandas.io.html (delayed), pandas.tests.io.test_html (delayed)
missing module named 'fsspec.registry' - imported by pandas.conftest (delayed), pandas.tests.io.test_fsspec (delayed)
missing module named 'py.path' - imported by pandas.tests.io.pytables.test_read (delayed), pandas.tests.io.sas.test_sas7bdat (delayed), pandas.tests.io.test_common (optional)
missing module named botocore - imported by pandas.io.common (delayed, conditional, optional), pandas.tests.io.parser.test_network (delayed)
missing module named 'xlrd.biffh' - imported by pandas.tests.io.excel.test_xlrd (delayed)
missing module named py - imported by pandas.tests.io.excel.test_readers (delayed)
missing module named xlrd - imported by pandas.io.excel._xlrd (delayed), pandas.io.excel._base (delayed, conditional), pandas.tests.io.excel.test_readers (delayed, conditional)
missing module named boto3 - imported by pandas.tests.io.conftest (delayed)
missing module named 'scipy.sparse' - imported by pandas.core.arrays.sparse.array (conditional), pandas.core.arrays.sparse.scipy_sparse (delayed, conditional), pandas.core.arrays.sparse.accessor (delayed), pandas.core.dtypes.common (delayed, conditional, optional), pandas.tests.arrays.sparse.test_accessor (delayed), pandas.tests.arrays.sparse.test_constructors (delayed), pandas.tests.dtypes.test_common (delayed, conditional), pandas.tests.indexing.test_loc (delayed)
missing module named numexpr - imported by pandas.core.computation.expressions (conditional), pandas.core.computation.engines (delayed), pandas.tests.computation.test_eval (delayed, conditional, optional)
missing module named dask - imported by pandas.tests.arrays.test_datetimelike (delayed, conditional)
missing module named lxml - imported by openpyxl.xml (delayed, optional), pandas.io.xml (conditional)
missing module named 'sqlalchemy.types' - imported by pandas.io.sql (delayed, conditional)
missing module named 'sqlalchemy.sql' - imported by pandas.io.sql (conditional)
missing module named tables - imported by pandas.io.pytables (delayed, conditional)
missing module named 'lxml.html' - imported by pandas.io.html (delayed)
missing module named markupsafe - imported by pandas.io.formats.style_render (top-level)
missing module named traitlets - imported by pandas.io.formats.printing (delayed, conditional)
missing module named IPython - imported by pandas.io.formats.printing (delayed)
missing module named xlsxwriter - imported by pandas.io.excel._xlsxwriter (delayed)
missing module named pyxlsb - imported by pandas.io.excel._pyxlsb (delayed)
missing module named defusedxml - imported by openpyxl.xml (delayed, optional)
missing module named 'defusedxml.ElementTree' - imported by openpyxl.xml.functions (conditional)
missing module named PIL - imported by openpyxl.drawing.image (optional)
missing module named 'odf.config' - imported by pandas.io.excel._odswriter (delayed)
missing module named 'odf.style' - imported by pandas.io.excel._odswriter (delayed)
missing module named 'odf.text' - imported by pandas.io.excel._odfreader (delayed), pandas.io.excel._odswriter (delayed)
missing module named 'odf.table' - imported by pandas.io.excel._odfreader (delayed), pandas.io.excel._odswriter (delayed)
missing module named 'odf.opendocument' - imported by pandas.io.excel._odfreader (delayed), pandas.io.excel._odswriter (delayed)
missing module named 'odf.element' - imported by pandas.io.excel._odfreader (delayed)
missing module named 'odf.namespaces' - imported by pandas.io.excel._odfreader (delayed)
missing module named odf - imported by pandas.io.excel._odfreader (delayed)
missing module named Foundation - imported by pandas.io.clipboard (delayed, conditional, optional)
missing module named AppKit - imported by pandas.io.clipboard (delayed, conditional, optional)
missing module named PyQt4 - imported by pandas.io.clipboard (delayed, conditional, optional)
missing module named qtpy - imported by pandas.io.clipboard (delayed, conditional, optional)
missing module named 'pyarrow.compute' - imported by pandas.core.arrays.string_arrow (conditional), pandas.core.arrays.arrow.array (conditional)
missing module named 'fsspec.implementations' - imported by pandas.conftest (delayed)
missing module named 'traitlets.config' - imported by pandas.conftest (delayed)
missing module named vms_lib - imported by platform (delayed, conditional, optional)
missing module named java - imported by platform (delayed)
missing module named _winreg - imported by platform (delayed, optional)
missing module named openpyxl.tests - imported by openpyxl.reader.excel (optional)
